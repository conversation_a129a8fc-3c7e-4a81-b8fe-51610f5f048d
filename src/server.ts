import  { macro } from "./lib/macro";
import { Host } from "./lib/master/masterLib";

const h = {
    ali_hk : new Host("ali_hk", "************", 22, "", "root"),
    ali_hk2 : new Host("ali_hk2", "**************", 22, "", "root"),
    ali_hk3 : new Host("ali_hk3", "************", 22, "", "root"),

    ali_de : new Host("ali_de", "************", 22, "", "root", true),
    ali_sg : new Host("ali_sg", "*************", 22, "", "root"),
    hz_hel : new Host("hz_hel2", "**************", 22, "", "root"),
    hz_fn : new Host("hz_fn", "***********", 22, "", "root"),

    bwg : new Host("bwg", "*************", 28192, "", "root"),

    aws_jp : new Host("aws_jp", "*************", 22, "aws-tokyo.pem", "admin", true),
    aws_kr : new Host("aws_kr", "**************", 22, "aws-kr.pem", "admin"),
    //aws_sg : new Host("aws_sg", "**************", 22, "aws-sg.pem", "admin"),
    //aws_fr : new Host("aws_fr", "**************", 22, "aws-fr.pem", "admin"),
    
    aws_oh : new Host("aws_oh", "************", 22, "aws-oh.pem", "admin", true),
    //aws_oh_a : new Host("aws_oh_a", "***********", 22, "aws-oh.pem", "admin", true),
    //aws_oh_b : new Host("aws_oh_b", "***********", 22, "aws-oh.pem", "admin", true),
    //aws_oh_c : new Host("aws_oh_c", "***********", 22, "aws-oh.pem", "admin", true),

    aws_ir   : new Host("aws_ir", "************", 22, "aws-ir.pem", "admin", true),
    aws_ir_b : new Host("aws_ir_b", "************", 22, "aws-ir.pem", "admin", true),

    //aws_ca_u : new Host("aws_ca_u", "*************", 22, "aws-ca-u.pem", "admin", true),
    //aws_or : new Host("aws_or", "*************", 22, "aws-or.pem", "admin", true),
    //aws_or_a : new Host("aws_or_a", "************", 22, "aws-or.pem", "admin", true),
    //aws_or_b : new Host("aws_or_b", "***********", 22, "aws-or.pem", "admin", true),
    //aws_or_c : new Host("aws_or_c", "***********", 22, "aws-or.pem", "admin", true),
    
    //aws_hk : new Host("aws_hk", "*************", 22, "aws-hk.pem", "admin"),
    aws_au : new Host("aws_au", "*************", 22, "aws-au.pem", "admin", true),
    aws_ca : new Host("aws_ca", "**************", 22, "aws-ca.pem", "admin", true),
    aws_vg : new Host("aws_vg", "**************", 22, "aws-vg.pem", "admin", true),

    aws_de : new Host("aws_de", "***********", 22, "aws-de.pem", "admin"),
    //aws_uk : new Host("aws_uk", "**********", 22, "aws-uk.pem", "admin"),
    //aws_sp : new Host("aws_sp", "************", 22, "aws-sp.pem", "admin"),

    aws_de_k : new Host("aws_de_k", "***********", 22, "", "ceil"),

    gc_mi : new Host("gc_mi", "**************", 22, "gc.pem", "ceil"),
    gc_nl : new Host("gc_nl", "************", 22, "gc.pem", "ceil"),
    gc_vg : new Host("gc_vg", "**************", 22, "gc.pem", "ceil"),
    gc_io : new Host("gc_io", "**************",22, "gc.pem", "ceil"),
    //gc_io2 : new Host("gc_io2", "**************",22, "gc.pem", "ceil"),
    //gc_io3 : new Host("gc_io3", "***************",22, "gc.pem", "ceil"),

    gc_sg : new Host("gc_sg", "**************",22, "gc.pem", "ceil"),

    gc_or : new Host("gc_or", "**************",22, "gc.pem", "ceil"),
    //gc_jp : new Host("gc_jp", "**************",22, "gc.pem", "ceil"),
    gc_fr : new Host("gc_fr", "**************",22, "gc.pem", "ceil"),
    gc_de2 : new Host("gc_de2", "************",22, "gc.pem", "ceil"),

    gc_la : new Host("gc_la", "*************",22, "gc.pem", "ceil"),

    az_us : new Host("az_us", "***************", 22, "", "ceil", true),
    az_jp : new Host("az_jp", "************", 22, "gc.pem", "ceil", true),

    home : new Host("local", "************", 22, "", "root"),
}

const config = {
    [macro.CHAIN.OEC] : [h.gc_sg],// h.local, h.ali_hk],
    [macro.CHAIN.REI] : [h.ali_hk],
    [macro.CHAIN.KAI] : [h.gc_sg,],
    [macro.CHAIN.KUB] : [h.ali_sg],

    [macro.CHAIN.HECO] : [h.aws_jp],
    //[macro.CHAIN.HECO] : [h.gc_jp, h.aws_jp],


    [macro.CHAIN.KCC] : [h.aws_jp],
    [macro.CHAIN.ASTR] : [h.ali_de],

    [macro.CHAIN.KLAY] : [h.aws_kr],

    [macro.CHAIN.GLMR] :[h.gc_nl, h.gc_vg, h.gc_fr,],
    [macro.CHAIN.MOVR] :[h.ali_de, h.gc_nl, h.gc_fr,],
    [macro.CHAIN.XDAI] :[h.ali_de, h.gc_nl, h.gc_fr, h.aws_de],

    [macro.CHAIN.POLYGON]:[h.ali_de],
    [macro.CHAIN.CELO]:[h.ali_de],
    [macro.CHAIN.ETHW]:[h.ali_de, h.gc_la],
    [macro.CHAIN.BSC] :[h.ali_de],
    //[macro.CHAIN.FITFI] :[h.ali_de],
    [macro.CHAIN.ONE] :[h.ali_de, h.gc_nl],


    [macro.CHAIN.MILKADA] :[h.gc_vg,],
    [macro.CHAIN.SAMA] :[h.gc_nl, h.gc_fr,],

    [macro.CHAIN.POM] :[h.gc_vg],
    [macro.CHAIN.AURORA] :[h.gc_vg,],
    [macro.CHAIN.LAT] :[h.gc_sg],
    [macro.CHAIN.VS] : [h.gc_vg,],
    [macro.CHAIN.ARB] : [h.ali_de],
    //[macro.CHAIN.NOVA] : [  h.aws_oh,  h.aws_ca,],
    //[macro.CHAIN.NOVA] : [ h.aws_fr],
    //[macro.CHAIN.ZK]:[h.aws_oh],
    [macro.CHAIN.METIS]:[h.ali_de, h.aws_de],

    [macro.CHAIN.CORE]:[h.aws_vg],
    //[macro.CHAIN.PLS] : [ h.ali_de, h.aws_de, h.gc_mi, h.aws_ir],
    //[macro.CHAIN.PLS] : [h.ali_de, h.aws_de],
    [macro.CHAIN.PLS] : [h.hz_hel],

    [macro.CHAIN.KAVA] : [h.aws_vg],
    [macro.CHAIN.BASE] : [h.aws_vg],
    [macro.CHAIN.OPBNB] : [h.aws_vg],
    [macro.CHAIN.WEMIX] : [h.aws_jp],
    [macro.CHAIN.ELA] : [h.gc_vg],
    [macro.CHAIN.ZETA] : [h.aws_vg],
    [macro.CHAIN.OP] : [h.gc_vg],
    [macro.CHAIN.MXC] : [h.gc_la, h.gc_vg],
    [macro.CHAIN.DEGEN] : [h.gc_la],
    [macro.CHAIN.BEVM] : [h.gc_vg],
    [macro.CHAIN.SEI] : [h.gc_sg],
    [macro.CHAIN.S] : [h.aws_vg],
    [macro.CHAIN.ABS] : [h.ali_de],
    [macro.CHAIN.X] : [h.ali_hk3],
    [macro.CHAIN.ECO] :[h.ali_hk3],
}

const arg = process.argv.splice(2);
const chain = arg[0] as macro.CHAIN;

if(arg[0] == "ls"){
    for(const [_name, _h] of Object.entries(h)){
        if(!_name.includes("gc")) continue;
        console.log(`${macro.COLOR.BGreen} ################### ${_name} ################### ${macro.COLOR.Off}`);
        _h.pm2_ls();
    }
    process.exit();
}

const hosts : Host[]= (config as any)[chain];
if(!hosts) throw(`### error chain or server: ${chain}`);

const action = arg[1];

function mainHost(){ return hosts[0];}

function main(action:string){
    switch(action){
        case "d" :        //从第一个服务器下载数据
            mainHost().d(chain);
            break;
        case "d_router": //从第一个服务器下载router
            mainHost().d_router(chain);
            break;
        case "u_bot" :   //上传bot到master
            mainHost().u_bot(chain);
            break;
        
        case "u_data" :  //上传数据到master，可能会误操作，暂时关闭
            mainHost().u_data(chain);
            break;

        case "u_router" : //上传路由到master
            mainHost().u_router(chain);
            break;
        case "u_all_bot" :
            hosts.forEach((_h,index) =>{
                if(index !== 0){
                    _h.u_bot(chain);
                    _h.pm2_restart(chain, true);
                }
            });
            break;
        case "u_all_router" :
            hosts.forEach((_h,index) =>{
                if(index !== 0){
                    _h.u_router(chain);
                    _h.pm2_restart(chain, true);
                }
            });
            break;
        case "u_all" : //上传bot,数据,路由到slave并且重启bot
            hosts.forEach((_h,index) =>{
                if(index !== 0){
                    _h.u_bot(chain);
                    _h.u_router(chain);
                    _h.pm2_restart(chain, true);
                }
            });
            break;
        case "test" :
            //mainHost().ssh(["ls -lha"]);
            //mainHost().ssh(["ls -lha"]);
            mainHost().pm2_restart(chain, false);
            break;
        
        case "stop_all":
            hosts.forEach((_h,index) =>{
                mainHost().pm2_stopall(chain, index !== 0);
            });
            break;

        default : throw("########## error action")
    }
}


main(action);